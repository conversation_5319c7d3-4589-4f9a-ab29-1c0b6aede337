#!/bin/bash

# Script to fix BILLmanager installation issues
# This script addresses common dependency conflicts and version mismatches

set -e

Info() {
    printf "\033[1;32m$@\033[0m\n"
}

Error() {
    printf "\033[1;31m$@\033[0m\n"
}

Warning() {
    printf "\033[1;35m$@\033[0m\n"
}

# Check if running as root
if [ "$(id -u)" != "0" ]; then
    Error "You must be root user to continue"
    exit 1
fi

# Detect OS type
if [ -f /etc/debian_version ]; then
    OSTYPE="DEBIAN"
elif [ -f /etc/redhat-release ] || [ -f /etc/centos-release ]; then
    OSTYPE="REDHAT"
else
    Error "Unsupported OS type"
    exit 1
fi

Info "Detected OS type: ${OSTYPE}"

# Function to fix dependency conflicts
fix_dependencies() {
    Info "Fixing dependency conflicts..."
    
    case ${OSTYPE} in
        DEBIAN)
            # Update package cache
            apt-get -y update
            
            # Fix broken packages
            apt-get -f install -y || :
            
            # Remove conflicting billmanager packages
            Info "Removing conflicting billmanager packages..."
            apt-get -y remove --purge \
                billmanager-plugin-libs \
                billmanager-plugin-gwlocalmail \
                billmanager-plugin-gwremotemail \
                billmanager-plugin-pmauto \
                billmanager-theme-client \
                2>/dev/null || :
            
            # Clean package cache
            apt-get clean
            apt-get autoclean
            
            # Update again
            apt-get -y update
            
            # Install required dependencies
            Info "Installing required dependencies..."
            apt-get -y install \
                coremanager-pkg-wkhtmltopdf \
                coremanager-pkg-nodejs \
                2>/dev/null || Warning "Some dependencies could not be installed"
            
            # Fix broken packages again
            apt-get -f install -y || :
            ;;
            
        REDHAT)
            # Clean yum cache
            yum clean all
            
            # Remove conflicting billmanager packages
            Info "Removing conflicting billmanager packages..."
            yum -y remove \
                billmanager-plugin-libs \
                billmanager-plugin-gwlocalmail \
                billmanager-plugin-gwremotemail \
                billmanager-plugin-pmauto \
                billmanager-theme-client \
                2>/dev/null || :
            
            # Install required dependencies
            Info "Installing required dependencies..."
            yum -y install \
                coremanager-pkg-wkhtmltopdf \
                coremanager-pkg-nodejs \
                2>/dev/null || Warning "Some dependencies could not be installed"
            ;;
    esac
}

# Function to downgrade coremanager if needed
downgrade_coremanager() {
    Info "Checking coremanager version..."
    
    case ${OSTYPE} in
        DEBIAN)
            # Get installed version
            INSTALLED_VER=$(dpkg -s coremanager 2>/dev/null | grep Version | awk '{print $2}' | cut -d- -f1)
            
            if [ -n "${INSTALLED_VER}" ]; then
                Info "Current coremanager version: ${INSTALLED_VER}"
                
                # Check if version is too high (> 5.349)
                MAJOR=$(echo "${INSTALLED_VER}" | cut -d. -f1)
                MINOR=$(echo "${INSTALLED_VER}" | cut -d. -f2)
                
                if [ "${MAJOR}" -eq 5 ] && [ "${MINOR}" -gt 349 ]; then
                    Warning "Coremanager version ${INSTALLED_VER} is too high for billmanager-corporate"
                    Info "Attempting to downgrade coremanager..."
                    
                    # Remove current version
                    apt-get -y remove --purge coremanager
                    
                    # Install compatible version
                    apt-get -y install coremanager=5.349.*
                    
                    if [ $? -eq 0 ]; then
                        Info "Successfully downgraded coremanager"
                    else
                        Warning "Could not downgrade coremanager automatically"
                    fi
                fi
            fi
            ;;
            
        REDHAT)
            # Get installed version
            INSTALLED_VER=$(rpm -q --qf "%{version}" coremanager 2>/dev/null)
            
            if [ -n "${INSTALLED_VER}" ]; then
                Info "Current coremanager version: ${INSTALLED_VER}"
                
                # Check if version is too high (> 5.349)
                MAJOR=$(echo "${INSTALLED_VER}" | cut -d. -f1)
                MINOR=$(echo "${INSTALLED_VER}" | cut -d. -f2)
                
                if [ "${MAJOR}" -eq 5 ] && [ "${MINOR}" -gt 349 ]; then
                    Warning "Coremanager version ${INSTALLED_VER} is too high for billmanager-corporate"
                    Info "Attempting to downgrade coremanager..."
                    
                    # Remove current version
                    yum -y remove coremanager
                    
                    # Install compatible version
                    yum -y install coremanager-5.349.*
                    
                    if [ $? -eq 0 ]; then
                        Info "Successfully downgraded coremanager"
                    else
                        Warning "Could not downgrade coremanager automatically"
                    fi
                fi
            fi
            ;;
    esac
}

# Main execution
Info "Starting BILLmanager installation fix..."

# Step 1: Fix dependencies
fix_dependencies

# Step 2: Downgrade coremanager if needed
downgrade_coremanager

# Step 3: Try to install billmanager-corporate
Info "Attempting to install billmanager-corporate..."

case ${OSTYPE} in
    DEBIAN)
        apt-get -y install billmanager-corporate
        ;;
    REDHAT)
        yum -y install billmanager-corporate
        ;;
esac

if [ $? -eq 0 ]; then
    Info "Successfully installed billmanager-corporate!"
else
    Error "Installation failed. Please check the error messages above."
    exit 1
fi

Info "Installation fix completed successfully!"
